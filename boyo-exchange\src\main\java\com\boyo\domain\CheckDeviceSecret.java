package com.boyo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 保密质检设备原始数据表
 * @TableName t_check_device_secret
 */
@TableName(value ="t_check_device_secret")
@Data
public class CheckDeviceSecret implements Serializable {
    /**
     * 重复条码
     */
    @TableId(value = "ppid")
    private String ppid;

    /**
     * 零件ID
     */
    @TableField(value = "part_id")
    private String partId;

    /**
     * 供应商
     */
    @TableField(value = "vendor")
    private String vendor;

    /**
     * 周数
     */
    @TableField(value = "wk")
    private String wk;

    /**
     * 测试时间
     */
    @TableField(value = "test_time")
    private String testTime;
    @TableField(exist = false)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date testTimeBegin;
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(exist = false)
    private Date testTimeEnd;

    /**
     * 测试结果
     */
    @TableField(value = "test_result")
    private String testResult;

    /**
     * 部门ID
     */
    @TableField(value = "dept_id")
    private String deptId;

    /**
     * 产线ID
     */
    @TableField(value = "line_id")
    private String lineId;

    /**
     * 工单ID
     */
    @TableField(value = "mo_id")
    private String moId;

    /**
     * 测试工站
     */
    @TableField(value = "test_station")
    private String testStation;

    /**
     * 设备序列号
     */
    @TableField(value = "machine_sn")
    private String machineSn;

    /**
     * 测试通道ID
     */
    @TableField(value = "test_channel_id")
    private String testChannelId;

    /**
     * 项目名称
     */
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 合格数
     */
    @TableField(value = "ok_count")
    private String okCount;

    /**
     * 不合格数
     */
    @TableField(value = "ng_count")
    private String ngCount;

    /**
     * 总数
     */
    @TableField(value = "total")
    private String total;

    /**
     * 良率(%)
     */
    @TableField(value = "yield")
    private String yield;

    /**
     * 网格
     */
    @TableField(value = "mesh")
    private String mesh;

    /**
     * 厚度
     */
    @TableField(value = "thickness")
    private String thickness;

    /**
     * 网疤
     */
    @TableField(value = "web_blemish_defect")
    private String webBlemishDefect;

    /**
     * 柱道
     */
    @TableField(value = "pillar_defect")
    private String pillarDefect;

    /**
     * 破洞
     */
    @TableField(value = "hole_defect")
    private String holeDefect;

    /**
     * 双线
     */
    @TableField(value = "double_line_defect")
    private String doubleLineDefect;

    /**
     * 打结
     */
    @TableField(value = "knot_defect")
    private String knotDefect;

    /**
     * 氧化
     */
    @TableField(value = "oxidation_defect")
    private String oxidationDefect;

    /**
     * 油污
     */
    @TableField(value = "oil_stain_defect")
    private String oilStainDefect;

    /**
     * 异物
     */
    @TableField(value = "foreign_object_defect")
    private String foreignObjectDefect;

    /**
     * 变形
     */
    @TableField(value = "deformation_defect")
    private String deformationDefect;

    /**
     * 裂口
     */
    @TableField(value = "crack_defect")
    private String crackDefect;

    /**
     * 异色
     */
    @TableField(value = "discoloration_defect")
    private String discolorationDefect;

    /**
     * 毛丝
     */
    @TableField(value = "hairiness_defect")
    private String hairinessDefect;

    /**
     * 接线头
     */
    @TableField(value = "connectorlug_defect")
    private String connectorlugDefect;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 算法版本
     */
    @TableField(value = "alog_version")
    private String alogVersion;
    /**
     * 标记点
     */
    @TableField(value = "markdot_defect")
    private String markdotDefect;
    /**
     * 测试时间间隔（分钟）
     */
    @TableField(value = "`interval`")
    private Long interval;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
