package com.boyo.controller.openapi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.PageQuery;
import com.boyo.common.core.domain.R;
import com.boyo.common.utils.redis.RedisUtils;
import com.boyo.domain.CheckDeviceSecret;
import com.boyo.domain.DeviceSecretState;
import com.boyo.domain.enums.DeviceSecretStateEnum;
import com.boyo.order.domain.dto.PostBodyDto;
import com.boyo.service.AoiDataAggregationService;
import com.boyo.service.CheckDeviceSecretService;
import com.boyo.service.DeviceSecretStateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 保密设备
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/device/check/secret")
public class DeviceSecretController extends BaseController {

    private String TOKEN = "G4TUNagBENcqpoKuOYMlBgKPqbQMQtIG";
    private String SECRET = "wnvkQk69zp5Q3xCXnxS47pBPL1CdT0Iy";

    @Autowired
    private CheckDeviceSecretService checkDeviceSecretService;

    @Autowired
    private AoiDataAggregationService aoiDataAggregationService;

    @Autowired
    private DeviceSecretStateService deviceSecretStateService;

    /**
     * 列表
     * @param checkDeviceSecret
     * @param pageQuery
     * @return
     */
    @GetMapping
    public R list(CheckDeviceSecret checkDeviceSecret, PageQuery pageQuery) {
        return R.ok(checkDeviceSecretService.page(pageQuery.build()
            , new LambdaQueryWrapper<CheckDeviceSecret>()
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getPpid()), CheckDeviceSecret::getPpid, checkDeviceSecret.getPpid())
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getPartId()), CheckDeviceSecret::getPartId, checkDeviceSecret.getPartId())
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getVendor()), CheckDeviceSecret::getVendor, checkDeviceSecret.getVendor())
                .eq(StrUtil.isNotEmpty(checkDeviceSecret.getWk()), CheckDeviceSecret::getWk, checkDeviceSecret.getWk())
                .eq(StrUtil.isNotEmpty(checkDeviceSecret.getTestResult()), CheckDeviceSecret::getTestResult, checkDeviceSecret.getTestResult())
                .eq(StrUtil.isNotEmpty(checkDeviceSecret.getDeptId()), CheckDeviceSecret::getDeptId, checkDeviceSecret.getDeptId())
                .eq(StrUtil.isNotEmpty(checkDeviceSecret.getLineId()), CheckDeviceSecret::getLineId, checkDeviceSecret.getLineId())
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getMoId()), CheckDeviceSecret::getMoId, checkDeviceSecret.getMoId())
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getTestStation()), CheckDeviceSecret::getTestStation, checkDeviceSecret.getTestStation())
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getMachineSn()), CheckDeviceSecret::getMachineSn, checkDeviceSecret.getMachineSn())
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getTestChannelId()), CheckDeviceSecret::getTestChannelId, checkDeviceSecret.getTestChannelId())
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getOkCount()), CheckDeviceSecret::getOkCount, checkDeviceSecret.getOkCount())
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getNgCount()), CheckDeviceSecret::getNgCount, checkDeviceSecret.getNgCount())
                .like(StrUtil.isNotEmpty(checkDeviceSecret.getProjectName()), CheckDeviceSecret::getProjectName, checkDeviceSecret.getProjectName())
                .between(ObjUtil.isNotEmpty(checkDeviceSecret.getTestTimeBegin()) && ObjUtil.isNotEmpty(checkDeviceSecret.getTestTimeEnd()), CheckDeviceSecret::getTestTime
                    , DateUtil.format(checkDeviceSecret.getTestTimeBegin(), "yyyy/MM/dd HH:mm"), DateUtil.format(checkDeviceSecret.getTestTimeEnd(), "yyyy/MM/dd HH:mm"))
                .orderByDesc(CheckDeviceSecret::getCreateTime)
        ));
    }

    /**
     * 获取最新数据
     * @return
     */
    @GetMapping("latest")
    public R getLatestByMachine() {
        List<CheckDeviceSecret> result = checkDeviceSecretService.getLatestByMachine();
        return R.ok(result);
    }

    /**
     * 重复校验
     */
    @PostMapping("repeatCheck")
    public Map<String, String> repeatCheck(@RequestHeader String token, @RequestBody PostBodyDto body) {
        Map<String, String> result = new HashMap<>();
        if (!TOKEN.equals(token)) {
            result.put("code", "OK");
            result.put("message", "token错误");
            return result;
        }
        if (!body.checkSign(SECRET)) {
            result.put("code", "OK");
            result.put("message", "签名错误");
            return result;
        }
        String[] ids = body.getData().split(",");
        List<String> idRepeat = new ArrayList<>();
        for (String id : ids) {
            CheckDeviceSecret byId = checkDeviceSecretService.getById(id);
            if (byId != null) {
                idRepeat.add(byId.getPpid());
            }
        }
        if (CollUtil.isNotEmpty(idRepeat)) {
            result.put("code", "NG");
            result.put("message", "当前序列号重复：" + CollUtil.join(idRepeat, ","));
            return result;
        }
        result.put("code", "OK");
        result.put("message", "当前序列号无重复");
        return result;
    }

    /**
     * 新增原始数据并实时更新日报表
     */
    @PostMapping("add")
    public R add(@RequestHeader String token, @RequestBody PostBodyDto body) {
        log.info("原始数据：{}", body.getData());
        if (!TOKEN.equals(token)) {
            throw new RuntimeException("token错误");
        }
        if (!body.checkSign(SECRET)) {
            throw new RuntimeException("签名错误");
        }

        List<CheckDeviceSecret> checkDeviceSecretList = JSON.parseArray(body.getData(), CheckDeviceSecret.class);
        List<String> failed = new ArrayList<>();
        List<String> savedMachines = new ArrayList<>();

        // 1. 保存原始数据
        for (CheckDeviceSecret checkDeviceSecret : checkDeviceSecretList) {
            // 更新间隔时间
            CheckDeviceSecret lastData = checkDeviceSecretService.getOne(new QueryWrapper<CheckDeviceSecret>()
                .orderByDesc("STR_TO_DATE(test_time, \"%Y/%m/%d %H:%i\")")
                .lambda().eq(CheckDeviceSecret::getMachineSn, checkDeviceSecret.getMachineSn()).last("limit 1")
            );
            long interval = DateUtil.between(DateUtil.parse(lastData.getTestTime(), "yyyy/MM/dd HH:mm")
                , DateUtil.parse(checkDeviceSecret.getTestTime(), "yyyy/MM/dd HH:mm"), DateUnit.MINUTE);
            checkDeviceSecret.setInterval(interval);
            if (!checkDeviceSecretService.save(checkDeviceSecret)) {
                failed.add(checkDeviceSecret.getPpid());
            } else {
                // 记录成功保存的设备，用于后续日报表更新
                if (!savedMachines.contains(checkDeviceSecret.getMachineSn())) {
                    savedMachines.add(checkDeviceSecret.getMachineSn());
                }
                log.debug("成功保存原始数据: ppid={}, machineSn={}",
                         checkDeviceSecret.getPpid(), checkDeviceSecret.getMachineSn());
            }
        }

        if (CollUtil.isNotEmpty(failed)) {
            return R.warn("新增失败：" + CollUtil.join(failed, ","));
        }

        // 2. 实时增量更新日报表数据
        if (CollUtil.isNotEmpty(savedMachines)) {
            try {
                // 根据实际保存的数据的测试时间确定报表日期
                // 获取第一条数据的测试时间作为报表日期
                String testTimeStr = checkDeviceSecretList.get(0).getTestTime();
                java.util.Date reportDate;

                try {
                    // 解析测试时间，提取日期部分 (格式: yyyy/MM/dd HH:mm)
                    java.util.Date testTime = DateUtil.parse(testTimeStr, "yyyy/MM/dd HH:mm");
                    // 获取测试时间的日期部分（去掉时分秒）
                    reportDate = DateUtil.beginOfDay(testTime);
                } catch (Exception e) {
                    log.warn("解析测试时间失败，使用当前日期: testTime={}, error={}", testTimeStr, e.getMessage());
                    // 如果解析失败，使用当前日期作为备用方案
                    java.time.LocalDate today = java.time.LocalDate.now();
                    reportDate = java.util.Date.from(today.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
                }

                log.info("开始实时增量更新日报表: reportDate={}, machines={}, 基于测试时间={}", reportDate, savedMachines, testTimeStr);

                // 对每个设备进行真正的增量更新
                int updateSuccessCount = 0;
                List<String> updateErrors = new ArrayList<>();

                for (String machineSn : savedMachines) {
                    try {
                        // 获取该设备新增的原始数据
                        List<CheckDeviceSecret> machineNewData = checkDeviceSecretList.stream()
                            .filter(data -> machineSn.equals(data.getMachineSn()))
                            .collect(Collectors.toList());

                        // 使用专门为add接口设计的增量更新方法
                        boolean success = aoiDataAggregationService.incrementalUpdateForNewRawData(reportDate, machineSn, machineNewData);
                        if (success) {
                            updateSuccessCount++;
                            log.debug("设备{}日报表增量更新成功，处理{}条新数据", machineSn, machineNewData.size());
                        } else {
                            updateErrors.add(machineSn + ": 增量更新失败");
                            log.warn("设备{}日报表增量更新失败", machineSn);
                        }
                    } catch (Exception e) {
                        updateErrors.add(machineSn + ": " + e.getMessage());
                        log.error("设备{}日报表增量更新异常", machineSn, e);
                    }
                }

                // 构建返回消息
                StringBuilder message = new StringBuilder("新增成功");
                if (updateSuccessCount > 0) {
                    message.append("，已实时更新").append(updateSuccessCount).append("台设备的日报表");
                }
                if (CollUtil.isNotEmpty(updateErrors)) {
                    message.append("，日报表更新失败: ").append(CollUtil.join(updateErrors, ","));
                }

                log.info("原始数据新增完成: 成功{}条，日报表更新成功{}台设备",
                        checkDeviceSecretList.size(), updateSuccessCount);

                return R.ok(message.toString());

            } catch (Exception e) {
                log.error("实时更新日报表时发生异常", e);
                return R.ok("新增成功，但日报表更新失败: " + e.getMessage());
            }
        }

        return R.ok("新增成功");
    }

    /**
     * 监控，直接保存到 redis 中
     */
    @PostMapping("monitor")
    public R monitor(@RequestHeader String token, @RequestBody PostBodyDto body) {
        if (!TOKEN.equals(token)) {
            throw new RuntimeException("token错误");
        }
        if (!body.checkSign(SECRET)) {
            throw new RuntimeException("签名错误");
        }
        try {
            List<DeviceSecretState> checkDeviceSecretList = JSON.parseArray(body.getData(), DeviceSecretState.class);
            for (DeviceSecretState deviceSecretState : checkDeviceSecretList) {
                log.info("设备状态更新开始：{}", JSON.toJSONString(deviceSecretState));
                // 留存记录
                deviceSecretStateService.save(deviceSecretState);
                // 用于实时查询
                String key = "device_secret_state:" + deviceSecretState.getMachineId();
                RedisUtils.setCacheObject(key, deviceSecretState);
                log.info("设备状态更新成功");
            }
        } catch (Exception e) {
            return R.ok("设备状态更新失败");
        }
        return R.ok("设备状态更新成功");
    }

    /**
     * 获取设备状态
     * @param deviceId
     * @return
     */
    @GetMapping("monitor/{deviceId}")
    public R monitorDeviceId(@PathVariable String deviceId) {
        return R.ok((DeviceSecretState) RedisUtils.getCacheObject("device_secret_state:" + deviceId));
    }

    /**
     * 获取设备状态列表
     * @return
     */
    @GetMapping("monitor/deviceState")
    public R monitorDeviceState() {
        return R.ok(DeviceSecretStateEnum.getStatus());
    }

    /**
     * 图表
     * @return
     */
    @GetMapping("chart")
    public R chart() {
        return R.ok(checkDeviceSecretService.getChartData());
    }

    /*
    CREATE TABLE `t_check_device_secret` (
      `part_id` varchar(255) DEFAULT NULL COMMENT '零件ID',
      `ppid` varchar(255) NOT NULL COMMENT '重复条码',
      `vendor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商',
      `wk` varchar(255) DEFAULT NULL COMMENT '周数',
      `test_time` varchar(255) DEFAULT NULL COMMENT '测试时间',
      `test_result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '测试结果',
      `dept_id` varchar(255) DEFAULT NULL COMMENT '部门ID',
      `line_id` varchar(255) DEFAULT NULL COMMENT '产线ID',
      `mo_id` varchar(255) DEFAULT NULL COMMENT '工单ID',
      `test_station` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '测试工站',
      `machine_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备序列号',
      `test_channel_id` varchar(255) DEFAULT NULL COMMENT '测试通道ID',
      `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目名称',
      `ok_count` varchar(255) DEFAULT NULL COMMENT '合格数',
      `ng_count` varchar(255) DEFAULT NULL COMMENT '不合格数',
      `total` varchar(255) DEFAULT NULL COMMENT '总数',
      `yield` varchar(255) DEFAULT NULL COMMENT '良率(%)',
      `mesh` varchar(255) DEFAULT NULL COMMENT '网格',
      `thickness` varchar(255) DEFAULT NULL COMMENT '厚度',
      `web_blemish_defect` varchar(255) DEFAULT NULL COMMENT '网疤',
      `pillar_defect` varchar(255) DEFAULT NULL COMMENT '柱道',
      `hole_defect` varchar(255) DEFAULT NULL COMMENT '破洞',
      `double_line_defect` varchar(255) DEFAULT NULL COMMENT '双线',
      `knot_defect` varchar(255) DEFAULT NULL COMMENT '打结',
      `oxidation_defect` varchar(255) DEFAULT NULL COMMENT '氧化',
      `oil_stain_defect` varchar(255) DEFAULT NULL COMMENT '油污',
      `foreign_object_defect` varchar(255) DEFAULT NULL COMMENT '异物',
      `deformation_defect` varchar(255) DEFAULT NULL COMMENT '变形',
      `crack_defect` varchar(255) DEFAULT NULL COMMENT '裂口',
      `discoloration_defect` varchar(255) DEFAULT NULL COMMENT '异色',
      `hairiness_defect` varchar(255) DEFAULT NULL COMMENT '毛丝',
      `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`ppid`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='保密质检设备原始数据表';
     */
}
