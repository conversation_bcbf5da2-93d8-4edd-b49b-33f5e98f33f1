package com.boyo.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.domain.CheckDeviceSecret;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_check_device_secret(保密质检设备原始数据表)】的数据库操作Mapper
* @createDate 2025-05-12 14:08:30
* @Entity generator.domain.CheckDeviceSecret
*/
public interface CheckDeviceSecretMapper extends BaseMapper<CheckDeviceSecret> {

    /**
     * 分页查询原始检测数据
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @param testDate 测试日期(可选)
     * @param machineSn 设备序列号(可选)
     * @param projectName 项目名称(可选)
     * @param testStation 测试站点(可选)
     * @param testResult 测试结果(可选)
     * @return 原始检测数据
     */
    List<Map<String, Object>> selectRawDataWithPaging(@Param("offset") Integer offset,
                                                      @Param("pageSize") Integer pageSize,
                                                      @Param("testDate") Date testDate,
                                                      @Param("machineSn") String machineSn,
                                                      @Param("projectName") String projectName,
                                                      @Param("testStation") String testStation,
                                                      @Param("testResult") String testResult);

    /**
     * 统计原始检测数据总数
     * @param testDate 测试日期(可选)
     * @param machineSn 设备序列号(可选)
     * @param projectName 项目名称(可选)
     * @param testStation 测试站点(可选)
     * @param testResult 测试结果(可选)
     * @return 总数
     */
    Integer countRawData(@Param("testDate") Date testDate,
                        @Param("machineSn") String machineSn,
                        @Param("projectName") String projectName,
                        @Param("testStation") String testStation,
                        @Param("testResult") String testResult);

    /**
     * 获取原始检测数据统计信息
     * @param testDate 测试日期(可选)
     * @param machineSn 设备序列号(可选)
     * @param projectName 项目名称(可选)
     * @param testStation 测试站点(可选)
     * @param testResult 测试结果(可选)
     * @return 统计信息
     */
    Map<String, Object> getRawDataStatistics(@Param("testDate") Date testDate,
                                            @Param("machineSn") String machineSn,
                                            @Param("projectName") String projectName,
                                            @Param("testStation") String testStation,
                                            @Param("testResult") String testResult);

    List<CheckDeviceSecret> getLatestByMachine();
}




