-- 为 t_aoi_daily_report_core 表添加增量更新跟踪字段
-- last_processed_raw_id: 记录最后处理的原始数据ID，用于增量更新

ALTER TABLE t_aoi_daily_report_core
ADD COLUMN last_processed_raw_id BIGINT DEFAULT 0 COMMENT '最后处理的原始数据ID，用于增量更新'
AFTER del_flag;

-- 为提高查询性能，添加索引
CREATE INDEX idx_aoi_core_date_machine ON t_aoi_daily_report_core(report_date, machine_sn);
CREATE INDEX idx_aoi_core_last_processed ON t_aoi_daily_report_core(last_processed_raw_id);

-- 初始化现有数据的 last_processed_raw_id 字段
-- 将现有记录的 last_processed_raw_id 设置为对应日期和设备的最大原始数据ID
UPDATE t_aoi_daily_report_core core
SET last_processed_raw_id = (
    SELECT IFNULL(MAX(raw.id), 0)
    FROM t_check_device_secret raw
    WHERE DATE(raw.test_time) = core.report_date
    AND raw.machine_sn = core.machine_sn
    AND raw.project_name = core.project_name
)
WHERE core.del_flag = 0;
