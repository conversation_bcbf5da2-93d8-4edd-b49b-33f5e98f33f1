package com.boyo.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.domain.CheckDeviceSecret;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_check_device_secret(保密质检设备原始数据表)】的数据库操作Service
* @createDate 2025-05-12 14:08:30
*/
public interface CheckDeviceSecretService extends IService<CheckDeviceSecret> {

    /**
     * 根据日期获取不重复的设备序列号列表
     * @param reportDate 报表日期
     * @return 设备序列号列表
     */
    List<String> getDistinctMachineSnByDate(Date reportDate);

    Map<String, Map<String, BigDecimal>> getChartData();

    List<CheckDeviceSecret> getLatestByMachine();
}
