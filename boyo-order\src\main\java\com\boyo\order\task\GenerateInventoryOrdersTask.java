package com.boyo.order.task;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.storage.TenantStorage;
import com.boyo.common.utils.StringUtils;
import com.boyo.order.domain.BaseMaterial;
import com.boyo.order.domain.TSalesOrder;
import com.boyo.order.domain.bo.SalesOrderAssBo;
import com.boyo.order.domain.bo.TSalesOrderHeaderBo;
import com.boyo.order.domain.bo.TSalesOrderMaterialBo;
import com.boyo.order.mapper.BaseMaterialMapper;
import com.boyo.order.mapper.TSalesOrderMapper;
import com.boyo.order.service.ITSalesOrderService;
import com.boyo.wms.domain.WmsStock;
import com.boyo.wms.mapper.WmsStockMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
@Slf4j
public class GenerateInventoryOrdersTask {
    private final WmsStockMapper wmsStockMapper;
    private final BaseMaterialMapper baseMaterialMapper;
    private final TSalesOrderMapper salesOrderMapper;
    private final ITSalesOrderService salesOrderService;
    
    
    /**
      * <AUTHOR>
      * @param
      * @return void
      * @remark 当库存不足时生成库存订单,每天早上6点执行
      * @date 2025/6/20 11:02
     */
    @Scheduled(cron = "0 0 6 * * *")
    //@Scheduled(cron = "0 0/2 * * * ?")
    private void GenerateInventoryOrders(){
        //查询库存表相同物料id的数据的数量并返回
        List<WmsStock> wmsStockList = wmsStockMapper.queryWmsStockListGroupingByMaterialId();
        try {
            if (ObjectUtil.isNotNull(wmsStockList) && wmsStockList.size()>0){
                ArrayList<String> orderStatuslist = new ArrayList<>();
                orderStatuslist.add("01");
                orderStatuslist.add("02");
                orderStatuslist.add("03");
                for (WmsStock stock : wmsStockList) {
                    BaseMaterial baseMaterial = baseMaterialMapper.selectById(stock.getMaterialId());
                    if (ObjectUtil.isNotNull(baseMaterial) && StringUtils.isNotBlank(baseMaterial.getMinCount())
                        && ObjectUtil.isNotNull(baseMaterial.getUnderstockCount())
                        && stock.getMaterialCount().compareTo(new BigDecimal(baseMaterial.getMinCount())) <= 0){
                        //并且订单表里没有该物料的正在执行或者新建或者已下发的订单
                        QueryWrapper<TSalesOrder> salesOrderQueryWrapper = new QueryWrapper<>();
                        salesOrderQueryWrapper.eq("material_id",stock.getMaterialId())
                            .eq("workorder_type",2)
                            .in("order_status",orderStatuslist);
                        List<TSalesOrder> tSalesOrderList = salesOrderMapper.selectList(salesOrderQueryWrapper);
                        if (tSalesOrderList.size() == 0){
                            //就生成一条库存订单，生成的数量为物料表的understockCount,编号要区分开
                            TSalesOrderHeaderBo tSalesOrderHeaderBo = new TSalesOrderHeaderBo();
                            tSalesOrderHeaderBo.setClientName("赛航");
                            TSalesOrderMaterialBo tSalesOrderMaterialBo = new TSalesOrderMaterialBo();
                            tSalesOrderMaterialBo.setMaterialId(baseMaterial.getId());
                            tSalesOrderMaterialBo.setOrderCount(new BigDecimal(baseMaterial.getUnderstockCount()));
                            ArrayList<TSalesOrderMaterialBo> tSalesOrderMaterialBos = new ArrayList<>();
                            tSalesOrderMaterialBos.add(tSalesOrderMaterialBo);
                            SalesOrderAssBo salesOrderAssBo = new SalesOrderAssBo();
                            salesOrderAssBo.setSalesOrderHeaderBo(tSalesOrderHeaderBo);
                            salesOrderAssBo.setSalesOrderBoList(tSalesOrderMaterialBos);
                            TenantStorage.setTenantId(1710998872911200258L);
                            salesOrderService.insertInventoryOrdersByBo(salesOrderAssBo);
                            log.info("生成一条库存订单，物料id为:{}",baseMaterial.getId()," 物料名称为:{}"+baseMaterial.getMaterialName());
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
    }
    
    
    
}
