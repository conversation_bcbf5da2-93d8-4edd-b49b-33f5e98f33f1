# AOI 增量更新测试指南

## 测试目标
验证新的增量更新逻辑是否正确工作，确保：
1. 新增数据能正确累加到现有记录
2. 不会重复统计同一条数据
3. 计算结果与完全重新计算一致

## 测试步骤

### 1. 准备测试数据

首先清空测试环境的AOI相关表：
```sql
-- 清空AOI相关表（注意：这会删除所有数据，请在测试环境执行）
DELETE FROM t_aoi_defect_detail;
DELETE FROM t_aoi_equipment_status;
DELETE FROM t_aoi_operator_info;
DELETE FROM t_aoi_statistics_summary;
DELETE FROM t_aoi_daily_report_core;
```

### 2. 第一次新增数据

通过 `/openapi/deviceSecret/add` 接口新增第一批数据：

```json
{
  "data": "[{\"ppid\":\"TEST001\",\"machine_sn\":\"A-1\",\"project_name\":\"V53\",\"test_time\":\"2025/06/21 10:00\",\"test_result\":\"OK\",\"total\":\"100\",\"ok_count\":\"1\",\"ng_count\":\"0\"},{\"ppid\":\"TEST002\",\"machine_sn\":\"A-1\",\"project_name\":\"V53\",\"test_time\":\"2025/06/21 10:01\",\"test_result\":\"NG\",\"total\":\"100\",\"ok_count\":\"0\",\"ng_count\":\"1\"}]",
  "timestamp": "1640995200000",
  "sign": "your_sign_here"
}
```

预期结果：
- Core表应该有1条记录：reportDate=2025-06-21, machineSn=A-1, projectName=V53
- inputCount=200, rollInput=2, okCount=1, ngCount=1
- controlledYield=50.00

### 3. 第二次新增数据（增量更新）

再次调用 add 接口，新增更多数据：

```json
{
  "data": "[{\"ppid\":\"TEST003\",\"machine_sn\":\"A-1\",\"project_name\":\"V53\",\"test_time\":\"2025/06/21 10:02\",\"test_result\":\"OK\",\"total\":\"100\",\"ok_count\":\"1\",\"ng_count\":\"0\"},{\"ppid\":\"TEST004\",\"machine_sn\":\"A-1\",\"project_name\":\"V53\",\"test_time\":\"2025/06/21 10:03\",\"test_result\":\"OK\",\"total\":\"100\",\"ok_count\":\"1\",\"ng_count\":\"0\"}]",
  "timestamp": "1640995200000",
  "sign": "your_sign_here"
}
```

预期结果：
- 同一条Core记录应该被更新（不是新增）
- inputCount=400, rollInput=4, okCount=3, ngCount=1
- controlledYield=75.00

### 4. 验证数据一致性

调用验证接口：
```
GET /aoi/dailyReport/validateIncrementalData?reportDate=2025-06-21&machineSn=A-1&projectName=V53
```

预期结果：
```json
{
  "success": true,
  "validation": {
    "valid": true,
    "message": "数据一致",
    "coreStats": {
      "inputCount": 400,
      "rollInput": 4,
      "okCount": 3,
      "ngCount": 1
    },
    "recalculatedStats": {
      "inputCount": 400,
      "rollInput": 4,
      "okCount": 3,
      "ngCount": 1
    }
  }
}
```

### 5. 测试不同项目

新增不同项目的数据：

```json
{
  "data": "[{\"ppid\":\"TEST005\",\"machine_sn\":\"A-1\",\"project_name\":\"V54\",\"test_time\":\"2025/06/21 10:04\",\"test_result\":\"OK\",\"total\":\"50\",\"ok_count\":\"1\",\"ng_count\":\"0\"}]",
  "timestamp": "1640995200000",
  "sign": "your_sign_here"
}
```

预期结果：
- 应该创建新的Core记录：projectName=V54
- 原有的V53项目记录不受影响

### 6. 性能测试

批量新增大量数据，测试增量更新的性能：

```json
{
  "data": "[{\"ppid\":\"PERF001\",\"machine_sn\":\"B-1\",\"project_name\":\"V53\",\"test_time\":\"2025/06/21 11:00\",\"test_result\":\"OK\",\"total\":\"100\",\"ok_count\":\"1\",\"ng_count\":\"0\"},...更多数据...]",
  "timestamp": "1640995200000",
  "sign": "your_sign_here"
}
```

观察：
- 响应时间应该比完全重新计算快
- 内存使用应该更少
- 数据库查询次数应该更少

## 测试检查点

### 数据正确性检查
1. ✅ 增量累加是否正确
2. ✅ 百分比字段重新计算是否正确
3. ✅ 不同项目是否正确分离
4. ✅ 验证接口是否能发现数据不一致

### 性能检查
1. ✅ 响应时间是否有改善
2. ✅ 数据库查询是否减少
3. ✅ 内存使用是否优化

### 边界情况检查
1. ✅ 空数据处理
2. ✅ 异常数据格式处理
3. ✅ 并发请求处理
4. ✅ 事务回滚测试

## 故障排查

如果测试失败，检查以下几点：

1. **数据不一致**：
   - 检查 calculateStatsFromRawData 方法的计算逻辑
   - 确认 updateExistingCoreRecord 方法的累加逻辑
   - 验证 recalculatePercentageFields 方法的计算公式

2. **性能问题**：
   - 检查数据库索引是否正确
   - 确认查询条件是否高效
   - 验证事务范围是否合理

3. **并发问题**：
   - 检查事务隔离级别
   - 确认锁机制是否正确
   - 验证数据版本控制

## 回滚方案

如果增量更新有问题，可以临时切换回原来的逻辑：

1. 在 DeviceSecretController.add 方法中，将：
   ```java
   aoiDataAggregationService.incrementalUpdateForNewRawData(reportDate, machineSn, machineNewData)
   ```
   
   改回：
   ```java
   aoiDataAggregationService.incrementalAggregateForMachine(reportDate, machineSn)
   ```

2. 这样就回到了原来的完全重新计算逻辑。
